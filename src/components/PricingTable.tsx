'use client'

import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { pricingPlans } from '@/lib/pricing'
import { useLanguage } from '@/context/LanguageContext'

export default function PricingTable() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })
  const { t, language } = useLanguage()

  // Commented out for future use - monthly pricing tab functionality
  // const [billingCycle, setBillingCycle] = useState(1) // 0 = 1 month, 1 = 3 months
  // const billingCycle = 0 // Fixed to 1-month pricing only - now using direct array index [0]



  return (
    <section className="section relative overflow-hidden" ref={ref}>
      {/* Grid pattern */}
      {/* <div className="absolute inset-0 opacity-10">
        <div className="grid grid-cols-12 gap-4 h-full">
          {Array.from({ length: 24 }).map((_, i) => (
            <div key={i} className="h-full w-px bg-white opacity-10" style={{ left: `${(i / 24) * 100}%` }}></div>
          ))}
          {Array.from({ length: 12 }).map((_, i) => (
            <div key={i} className="h-px w-full bg-white opacity-10" style={{ top: `${(i / 12) * 100}%` }}></div>
          ))}
        </div>
      </div> */}

      <div className="relative z-10">
        <div className="text-center mb-12">
          <motion.h2
            className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 font-title"
            dangerouslySetInnerHTML={{ __html: t('pricing_title') }}
            initial={{ opacity: 0, y: 30 }}
            animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.6, delay: 0.1, ease: "easeOut" }}
          />
          <motion.p
            className="max-w-2xl mx-auto text-base sm:text-lg font-body mb-4"
            initial={{ opacity: 0, y: 30 }}
            animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
          >
            {t('pricing_subtitle')}
          </motion.p>
          {/* Promotional message */}
          <motion.div
            className="max-w-3xl mx-auto p-4 rounded-2xl mb-6 relative overflow-hidden"
            style={{
              background: 'linear-gradient(135deg, rgba(134, 107, 255, 0.15) 0%, rgba(134, 107, 255, 0.05) 100%)',
              border: '1px solid rgba(134, 107, 255, 0.3)',
              boxShadow: '0 8px 32px rgba(134, 107, 255, 0.1), 0 0 0 1px rgba(134, 107, 255, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
            }}
            initial={{ opacity: 0, y: 30 }}
            animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.6, delay: 0.3, ease: "easeOut" }}
          >
            <div className="absolute inset-0 bg-gradient-to-br from-jade-purple/5 to-transparent opacity-50 rounded-2xl"></div>
            <p className="text-sm sm:text-base font-body text-jade-purple-light relative z-10">
              {t('pricing_promo')}
            </p>
          </motion.div>
        </div>

        {/* Commented out for future use - billing cycle selection */}
        {/*
        <motion.div
          className="mb-8 flex justify-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="inline-flex p-2 border-white/30 border rounded-xl" style={{
            backgroundColor: 'rgba(255, 255, 255, 0.025)',
            boxShadow: '0 0 10px rgba(255, 255, 255, 0.15), inset 0 0 20px rgba(255, 255, 255, 0.15)'
          }}>
            {billingCycleKeys.map((cycleKey, index) => (
              <motion.button
                key={index}
                className={`py-3 px-6 text-base font-medium font-body ${
                  billingCycle === index
                    ? 'btn-glass-filled-flat'
                    : 'btn-glass-outline-flat'
                }`}
                style={{
                  paddingTop: '0.75rem',
                  paddingBottom: '0.75rem',
                  paddingLeft: '1.5rem',
                  paddingRight: '1.5rem',
                  marginRight: '0.25rem',
                  marginLeft: '0.25rem'
                }}
                onClick={() => setBillingCycle(index)}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                {t(cycleKey)}
              </motion.button>
            ))}
          </div>
        </motion.div>
        */}

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6 px-6 sm:px-0">
          {pricingPlans.map((plan, index) => (
            <motion.div
              key={index}
              className={`relative bg-gradient-to-br overflow-hidden flex flex-col rounded-2xl p-6 border transition-colors duration-300 ${
                plan.isPopular
                  ? 'from-jade-purple/[0.15] to-jade-purple/[0.05] border-jade-purple/40 hover:border-jade-purple/70'
                  : 'from-white/[0.08] to-white/[0.02] border-white/20 hover:border-white/50'
              }`}
              style={{
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(255, 255, 255, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
              }}
              initial={{ opacity: 0, y: 30 }}
              animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{
                duration: 0.6,
                delay: 0.4 + index * 0.1,
                ease: "easeOut"
              }}
            >

              {plan.isPopular && (
                <div className="absolute top-4 right-4 bg-jade-purple/80 backdrop-blur-sm text-white text-xs px-3 py-1.5 rounded-full font-body border border-jade-purple/40"
                  style={{
                    boxShadow: '0 4px 16px rgba(134, 107, 255, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.2)'
                  }}>
                  {t('recommended')}
                </div>
              )}
              <div className="mb-4 relative">
                <h3 className="text-xl font-bold font-title">{t(plan.nameKey)}</h3>
                {plan.descriptionKey && <p className="text-sm text-gray-400 font-body mt-1">{t(plan.descriptionKey)}</p>}
              </div>

              {plan.isAddon ? (
                <>
                  <div className="flex-grow">
                    <div className="space-y-6 mt-2 relative">
                      {/* Three pricing tier cards */}
                      {plan.prices.map((price, tierIndex) => (
                        <div key={tierIndex} className="p-4 rounded-xl border border-white/20" style={{
                          boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)'
                        }}>
                          <div className="text-sm font-body text-zinc-300 text-center">
                            {language === 'kh' ? (
                              <>
                                {plan.pricesKh[tierIndex].toLocaleString()}៛ {t('for')} {plan.addonTextKh[tierIndex]}
                              </>
                            ) : (
                              <>
                                ${price} for additional {plan.addonText[tierIndex]}
                              </>
                            )}
                          </div>
                        </div>
                      ))}

                      {/* Lower price text with get in touch button */}
                      <div className="p-4 rounded-xl bg-jade-purple/[0.1] border border-jade-purple/30 backdrop-blur-sm" style={{
                        boxShadow: '0 4px 16px rgba(134, 107, 255, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
                      }}>
                        <p className="font-body text-sm text-zinc-300 text-center mb-3">
                          {language === 'kh' ? 'មានការបញ្ចុះតម្លៃថោកជាងនេះសម្រាប់បរិមាណសារច្រើន' : 'Lower price for high-volume'}
                        </p>
                        <a
                          href="https://t.me/chhlatbot"
                          target="_blank"
                          className="w-full py-2 rounded-xl font-medium transition duration-300 text-white text-center block bg-jade-purple/20 hover:bg-jade-purple/40 border border-jade-purple/50 hover:border-jade-purple/70"
                        >
                          {t('get_in_touch')}
                        </a>
                      </div>
                    </div>
                  </div>

                  <div className="mt-auto">

                  </div>
                </>
              ) : (
                <>
                  <div className="mb-2 relative">
                    <div className="flex flex-col">
                      {plan.discounts[0] && plan.discounts[0] !== '' ? (
                        <>
                          <div className="flex items-baseline">
                            <span className="text-5xl font-bold font-title">
                              {language === 'kh' ? (
                                <>
                                  {plan.pricesKh[0].toLocaleString()}៛
                                </>
                              ) : (
                                <>
                                  ${plan.prices[0]}
                                </>
                              )}
                            </span>
                            <span className="text-sm text-gray-400 ml-1"></span>
                          </div>
                          <div>
                            <span className="line-through text-gray-500 text-lg">
                              {language === 'kh' ? (
                                <>
                                  {(plan.basePrice * 4000).toLocaleString()}៛
                                </>
                              ) : (
                                <>
                                  ${plan.basePrice}
                                </>
                              )}
                            </span>
                          </div>
                          <span className="text-xs bg-jade-purple/50 text-white px-3 py-2 rounded-full absolute top-0 right-0 flex flex-col items-center">
                            {plan.discounts[0]}
                            <span>OFF</span>
                          </span>
                        </>
                      ) : (
                        <>
                          <span className="text-5xl font-bold font-title">
                            {language === 'kh' ? (
                              <>
                                {plan.pricesKh[0].toLocaleString()}៛
                              </>
                            ) : (
                              <>
                                ${plan.prices[0]}
                              </>
                            )}
                          </span>
                        </>
                      )}
                    </div>
                    {/* Commented out as requested
                    {billingCycle > 0 && !plan.isAddon && (
                      <p className="text-xs text-gray-500 mt-1">
                        {t('billed_for')
                          .replace('${amount}', String(typeof plan.prices[billingCycle] === 'number' ? plan.prices[billingCycle] * 3 : 0))
                          .replace('${months}', String('3'))}
                      </p>
                    )}
                    */}
                  </div>
                  <div className="space-y-3 mb-6 flex-grow relative">
                    <div className="flex items-center">
                      <span className="font-medium mr-2 font-body text-base sm:text-base">{t('messages_label')}</span>
                      <span className="font-body text-sm sm:text-base">
                        {language === 'kh' && plan.messagesKh ? plan.messagesKh : plan.messages}
                      </span>
                    </div>
                    <div className="flex items-center">
                      <span className="font-medium mr-2 font-body text-base sm:text-base">{t('channels_label')}</span>
                      <span className="font-body text-sm sm:text-base">
                        {language === 'kh' && plan.accountsKh ? plan.accountsKh : plan.accounts}
                      </span>
                    </div>
                    <div>
                      <span className="font-medium block mb-1 font-body text-base sm:text-base">{t('features_label')}</span>
                      <ul className="space-y-1 text-xs sm:text-sm font-body">
                        {plan.featureKeys?.map((featureKey, idx) => (
                          <li key={idx} className="flex items-start">
                            <span className="text-jade-purple mr-2">➤</span>
                            {t(featureKey)}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                  <a
                    href={index === 2 ? '#' : `/register?plan=${encodeURIComponent(plan.nameKey.replace('plan_', ''))}`}
                    className={`w-full py-2 rounded-md font-medium transition duration-300 mt-auto relative z-10 text-center block ${
                      plan.isPopular
                        ? 'btn-primary'
                        : index === 2
                          ? 'btn-secondary opacity-60 cursor-not-allowed'
                          : 'btn-secondary'
                    }`}
                    onClick={(e) => index === 2 && e.preventDefault()}
                  >
                    {index === 2 ? t('coming_soon_plan') : t('try_free')}
                  </a>
                </>
              )}
            </motion.div>
          ))}
        </div>

        <div className="text-center mt-6 text-sm text-gray-500 font-body">
          <p>{t('pricing_note')}</p>
        </div>
      </div>
    </section>
  )
}
