import { NextRequest, NextResponse } from 'next/server'
import { createServerComponentSupabase } from '@/utils/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerComponentSupabase()

    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      console.error('Authentication error:', userError)
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Parse request body
    const { platform, platformPrefix, platformIdentifier } = await request.json()

    if (!platform || !platformPrefix || !platformIdentifier) {
      return NextResponse.json(
        { error: 'Missing required fields: platform, platformPrefix, platformIdentifier' },
        { status: 400 }
      )
    }

    // Get client ID from user
    const { data: clientData, error: clientError } = await supabase
      .from('clients')
      .select('client_id')
      .eq('auth_id', user.id)
      .single()

    if (clientError || !clientData) {
      console.error('Error fetching client data:', clientError)
      return NextResponse.json(
        { error: 'Client not found' },
        { status: 404 }
      )
    }

    const clientId = clientData.client_id

    // Send disconnect data to webhook
    try {
      const webhookUrl = process.env.DISCONNECT_PLATFORM_WEBHOOK_URL
      
      if (!webhookUrl) {
        console.error('DISCONNECT_PLATFORM_WEBHOOK_URL not configured')
        return NextResponse.json(
          { error: 'Webhook URL not configured' },
          { status: 500 }
        )
      }

      // Prepare webhook payload
      const webhookPayload = {
        platformPrefix: platformPrefix,
        platformIdentifier: platformIdentifier,
        action: 'Disconnect-Platform',
        source: 'chhlatbot-dashboard',
        version: '1.0'
      }

      console.log('Sending disconnect data to webhook:', {
        platform,
        platformPrefix,
        platformIdentifier,
        action: 'Disconnect-Platform'
      })

      // Send to webhook
      const webhookResponse = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(webhookPayload),
      })

      if (!webhookResponse.ok) {
        console.error('Webhook request failed:', {
          status: webhookResponse.status,
          statusText: webhookResponse.statusText,
          platform,
          platformPrefix,
          platformIdentifier
        })
        
        return NextResponse.json(
          { error: 'Failed to send disconnect data to webhook' },
          { status: 500 }
        )
      } else {
        console.log('Platform disconnect data sent to webhook successfully:', {
          platform,
          platformPrefix,
          platformIdentifier,
          action: 'Disconnect-Platform',
          webhookStatus: webhookResponse.status
        })
      }

      return NextResponse.json({
        success: true,
        message: `Platform ${platform} disconnect data sent successfully`,
        data: {
          platform,
          platformPrefix,
          platformIdentifier,
          action: 'Disconnect-Platform'
        }
      })

    } catch (webhookError) {
      console.error('Error sending disconnect data to webhook:', webhookError)
      return NextResponse.json(
        { error: 'Failed to send disconnect data to webhook' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Unexpected error in platform disconnect:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
