import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    // Parse request body
    const body = await request.json();
    const { code, plan, username, lang, ttl, timestamp, action } = body;

    // Validate required parameters
    if (!code || !plan || !username || !lang || !ttl || !timestamp || !action) {
      return NextResponse.json(
        { error: 'Missing required parameters: code, plan, username, lang, ttl, timestamp, action' },
        { status: 400 }
      );
    }

    // Get webhook URL from environment variables
    const webhookUrl = process.env.SET_REGISTER_WEBHOOK_URL;
    
    if (!webhookUrl) {
      console.error('SET_REGISTER_WEBHOOK_URL environment variable is not set');
      return NextResponse.json(
        { error: 'Webhook configuration missing' },
        { status: 500 }
      );
    }

    // Prepare secure payload for webhook
    const webhookPayload = {
      code,
      plan,
      username,
      lang,
      ttl,
      timestamp,
      action,
      source: 'chhlatbot-dashboard',
      version: '1.0'
    };

    // Send data to webhook
    const webhookResponse = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'ChhlatBot-Registration/1.0',
      },
      body: JSON.stringify(webhookPayload),
    });

    if (!webhookResponse.ok) {
      const errorText = await webhookResponse.text();
      console.error('Webhook request failed:', {
        status: webhookResponse.status,
        statusText: webhookResponse.statusText,
        body: errorText
      });
      
      return NextResponse.json(
        { error: 'Failed to store registration data' },
        { status: 500 }
      );
    }

    // Try to parse webhook response
    let webhookData;
    try {
      webhookData = await webhookResponse.json();
    } catch (parseError) {
      // If response is not JSON, that's okay as long as status was ok
      console.log('Webhook response was not JSON, but request succeeded');
    }

    console.log('Registration data sent to webhook successfully:', {
      code,
      username,
      plan,
      lang,
      webhookStatus: webhookResponse.status
    });

    return NextResponse.json({ 
      success: true,
      message: 'Registration data stored successfully'
    });

  } catch (error) {
    console.error('Error in register webhook API:', error);
    return NextResponse.json(
      { error: 'Failed to store registration data' },
      { status: 500 }
    );
  }
}
